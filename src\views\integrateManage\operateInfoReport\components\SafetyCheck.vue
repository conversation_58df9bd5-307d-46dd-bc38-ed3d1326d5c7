<template>
  <div class="safety-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="自检完成率信息"></SubTitle>
      </el-col>
      <el-col :xl="12">
        <TimeScreen class="time-screen"></TimeScreen>
      </el-col>
    </el-row>
    <main class="safety-main">
      <div class="safety-main-left">
        <div class="chart" id="safety-chart"></div>
      </div>
      <div class="safety-main-right">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }">
          <el-table-column prop="plateNumber" label="司机类型" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="应检次数" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="实检次数" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="完成率" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="正常率" align="center"></el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import * as echarts from "echarts";

  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        tableData: [],
        chartInstance: null,
        chartData: {
          categories: ["全体司机", "大型床位司机", "小型床位司机", "小诊所司机"],
          completionRates: [85, 92, 78, 65], // 自检完成率
          normalRates: [85, 42, 95, 52], // 自检正常率
        },
      };
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
    beforeDestroy() {
      if (this.chartInstance) {
        this.chartInstance.dispose();
      }
    },
    methods: {
      initChart() {
        const chartDom = document.getElementById("safety-chart");
        this.chartInstance = echarts.init(chartDom);

        const option = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#999",
              },
            },
            formatter: function (params) {
              let result = params[0].name + "<br/>";
              params.forEach((param) => {
                if (param.seriesName === "自检完成率") {
                  result += `${param.seriesName}: ${param.value}%<br/>`;
                } else if (param.seriesName === "自检正常率") {
                  result += `${param.seriesName}: ${param.value}%<br/>`;
                }
              });
              return result;
            },
          },
          legend: {
            data: [
              {
                name: "完成率≥90%",
                icon: "rect",
                itemStyle: {
                  color: "#10b981",
                },
              },
              {
                name: "完成率<90%",
                icon: "rect",
                itemStyle: {
                  color: "#f59e0b",
                },
              },
              {
                name: "完成率<80%",
                icon: "rect",
                itemStyle: {
                  color: "#ef4444",
                },
              },
              {
                name: "自检正常率",
                icon: "path://M0,0 L20,0",
                itemStyle: {
                  color: "#2196F3",
                },
              },
            ],
            bottom: 10,
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
              color: "#666",
              fontSize: 12,
            },
          },
          grid: {
            top: "10%",
            left: "3%",
            right: "4%",
            bottom: "15%",
            containLabel: true,
          },
          xAxis: [
            {
              type: "category",
              data: this.chartData.categories,
              axisPointer: {
                type: "shadow",
              },
              axisLabel: {
                color: "#666",
                fontSize: 12,
                interval: 0,
                rotate: 0,
              },
              axisLine: {
                lineStyle: {
                  color: "#e6e6e6",
                },
              },
              axisTick: {
                show: false,
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "自检完成率(%)",
              min: 0,
              max: 100,
              position: "left",
              axisLabel: {
                formatter: "{value}%",
                color: "#666",
                fontSize: 12,
              },
            },
            {
              type: "value",
              name: "自检正常率(%)",
              min: 0,
              max: 100,
              position: "right",
              axisLabel: {
                formatter: "{value}%",
                color: "#666",
                fontSize: 12,
              },
            },
          ],
          series: [
            {
              name: "自检完成率",
              type: "bar",
              yAxisIndex: 0,
              data: this.chartData.completionRates,
              barWidth: "40%",
              itemStyle: {
                color: function (params) {
                  const value = params.value;
                  if (value >= 90) {
                    return "#10b981"; // 绿色：完成率>=90%
                  }
                  if (value >= 80) {
                    return "#f59e0b"; // 橙色：完成率80-89%
                  }
                  return "#ef4444"; // 红色：完成率<80%
                },
              },
              label: {
                show: true,
                position: "top",
                formatter: "{c}%",
                color: "#666",
                fontSize: 12,
              },
            },
            {
              name: "自检正常率",
              type: "line",
              yAxisIndex: 1,
              data: this.chartData.normalRates,
              lineStyle: {
                color: "#2196F3",
                width: 3,
              },
              itemStyle: {
                color: "#ffffff",
                borderColor: "#2196F3",
                borderWidth: 2,
              },
              symbol: "circle",
              symbolSize: 8,
              label: {
                show: false,
              },
            },
          ],
        };

        this.chartInstance.setOption(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .safety-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .time-screen {
    justify-content: flex-end;
  }
  .safety-main {
    display: flex;
    .safety-main-left {
      width: 34%;
      height: 500px;
      .chart {
        width: 100%;
        height: 100%;
      }
    }
    .safety-main-right {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
