<template>
  <div class="safety-container">
    <el-row class="mb-12">
      <el-col :xl="12">
        <SubTitle title="自检完成率信息"></SubTitle>
      </el-col>
      <el-col :xl="12">
        <TimeScreen class="time-screen"></TimeScreen>
      </el-col>
    </el-row>
    <main class="safety-main">
      <div class="safety-main-left">
        <div class="chart" id="safety-chart"></div>
      </div>
      <div class="safety-main-right">
        <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }">
          <el-table-column prop="plateNumber" label="司机类型" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="应检次数" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="实检次数" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="完成率" align="center"></el-table-column>
          <el-table-column prop="operatorName" label="正常率" align="center"></el-table-column>
        </el-table>
      </div>
    </main>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    data() {
      return {
        tableData: [],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .safety-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
  }
  .time-screen {
    justify-content: flex-end;
  }
  .safety-main {
    display: flex;
    .safety-main-left {
      width: 400px;
      height: 500px;
      .chart {
        width: 100%;
        height: 100%;
      }
    }
    .safety-main-right {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
